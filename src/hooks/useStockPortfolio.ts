import { useState, useEffect, useCallback } from 'react';
import { StockService } from '../services/stockService';
import { WebSocketService } from '../services/websocketService';
import { 
  UserHolding, 
  StockRealtimeData, 
  StockHoldingWithRealtimeData,
  PortfolioSummary
} from '../types/stock';

export const useStockPortfolio = () => {
  const [holdings, setHoldings] = useState<UserHolding[]>([]);
  const [realtimeData, setRealtimeData] = useState<Record<string, StockRealtimeData>>({});
  const [combinedData, setCombinedData] = useState<StockHoldingWithRealtimeData[]>([]);
  const [portfolioSummary, setPortfolioSummary] = useState<PortfolioSummary>({
    totalInvestment: 0,
    currentValue: 0,
    totalProfitLoss: 0,
    profitLossPercentage: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const stockService = StockService.getInstance();
  const websocketService = WebSocketService.getInstance();

  // Fetch user holdings
  const fetchHoldings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await stockService.getUserHoldings();
      setHoldings(data);
      return data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch holdings');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Handle realtime data updates
  const handleRealtimeUpdate = useCallback((stockCode: string, data: StockRealtimeData) => {
    setRealtimeData(prev => ({
      ...prev,
      [stockCode]: data
    }));
  }, []);

  // Subscribe to WebSocket for each holding
  const subscribeToHoldings = useCallback((holdingsData: UserHolding[]) => {
    if (!websocketService.isConnected()) {
      websocketService.connect().then(() => {
        holdingsData.forEach(holding => {
          websocketService.subscribeToStock(
            holding.stock_code,
            (data) => handleRealtimeUpdate(holding.stock_code, data)
          );
        });
      }).catch(err => {
        setError('Failed to connect to WebSocket');
        console.error('WebSocket connection error:', err);
      });
    } else {
      holdingsData.forEach(holding => {
        websocketService.subscribeToStock(
          holding.stock_code,
          (data) => handleRealtimeUpdate(holding.stock_code, data)
        );
      });
    }
  }, [handleRealtimeUpdate]);

  // Combine holdings with realtime data
  const combineData = useCallback(() => {
    if (holdings.length === 0) return;

    const combined = holdings.map(holding => {
      const stockData = realtimeData[holding.stock_code];
      // Convert value from thousands VND to actual VND (value * 1000)
      const buyPrice = holding.value * 1000;
      // Current price from WebSocket is already in VND
      const currentPrice = stockData?.p || buyPrice;
      const totalValue = holding.quantity * buyPrice;
      const currentValue = holding.quantity * currentPrice;
      const profitLoss = currentValue - totalValue;
      const profitLossPercentage = totalValue > 0 ? (profitLoss / totalValue) * 100 : 0;

      return {
        id: holding.id,
        stockCode: holding.stock_code,
        quantity: holding.quantity,
        buyPrice,
        currentPrice,
        totalValue,
        currentValue,
        profitLoss,
        profitLossPercentage,
        realtimeData: stockData
      };
    });

    setCombinedData(combined);

    // Calculate portfolio summary
    const totalInvestment = combined.reduce((sum, item) => sum + item.totalValue, 0);
    const currentValue = combined.reduce((sum, item) => sum + item.currentValue, 0);
    const totalProfitLoss = currentValue - totalInvestment;
    const profitLossPercentage = totalInvestment > 0 ? (totalProfitLoss / totalInvestment) * 100 : 0;

    setPortfolioSummary({
      totalInvestment,
      currentValue,
      totalProfitLoss,
      profitLossPercentage
    });
  }, [holdings, realtimeData]);

  // Initialize data on component mount
  useEffect(() => {
    const init = async () => {
      const holdingsData = await fetchHoldings();
      if (holdingsData.length > 0) {
        subscribeToHoldings(holdingsData);
      }
    };

    init();

    return () => {
      // Cleanup WebSocket connection on unmount
      websocketService.disconnect();
    };
  }, [fetchHoldings, subscribeToHoldings]);

  // Update combined data whenever holdings or realtime data changes
  useEffect(() => {
    combineData();
  }, [holdings, realtimeData, combineData]);

  return {
    holdings: combinedData,
    portfolioSummary,
    loading,
    error,
    refreshHoldings: fetchHoldings
  };
};
