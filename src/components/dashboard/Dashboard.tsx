import React from 'react';
import { Header } from '../layout/Header';
import { PortfolioSummary } from '../portfolio/PortfolioSummary';
import { HoldingsList } from '../portfolio/HoldingsList';
import { useStockPortfolio } from '../../hooks/useStockPortfolio';
import { AlertCircle, RefreshCw } from 'lucide-react';

export const Dashboard: React.FC = () => {
  const { holdings, portfolioSummary, loading, error, refreshHoldings } = useStockPortfolio();

  return (
    <div className="min-h-screen bg-slate-950">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-2xl font-bold text-white mb-2">Dashboard</h2>
            <p className="text-slate-400"><PERSON> d<PERSON><PERSON> danh mục đầu tư của bạn</p>
          </div>

          <button
            type="button"
            onClick={refreshHoldings}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-slate-800 hover:bg-slate-700 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Làm mới</span>
          </button>
        </div>

        {error && (
          <div className="mb-8 p-4 bg-red-900/20 border border-red-900/50 rounded-lg flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
            <p className="text-red-400">{error}</p>
          </div>
        )}

        {/* Portfolio Summary */}
        <PortfolioSummary summary={portfolioSummary} loading={loading} />

        <div className="grid grid-cols-1 gap-8">
          {/* Holdings List */}
          <HoldingsList
            holdings={holdings}
            loading={loading}
            onRefresh={refreshHoldings}
          />
        </div>
      </main>
    </div>
  );
};