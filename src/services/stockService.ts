import { UserHolding } from '../types/stock';
import { AuthService } from './authService';

const API_BASE_URL = 'https://index-be.daihiep.click/api';

export class StockService {
  private static instance: StockService;
  private authService: AuthService;
  
  constructor() {
    this.authService = AuthService.getInstance();
  }
  
  static getInstance(): StockService {
    if (!StockService.instance) {
      StockService.instance = new StockService();
    }
    return StockService.instance;
  }

  private getAuthHeaders(): HeadersInit {
    const token = this.authService.getAccessToken();
    return {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    };
  }

  async getUserHoldings(): Promise<UserHolding[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/user_holdings/`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Unauthorized - Please login again');
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to fetch user holdings');
      }

      const data: UserHolding[] = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching user holdings:', error);
      throw error;
    }
  }
}
